@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Orbitron:wght@400..900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    background: #0d0d0d;
    overflow-x: hidden !important;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-[#1A1A1A];
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-neon-green rounded-full;
    box-shadow: 0 0 10px rgba(0, 255, 163, 0.5);
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neon-blue;
  }
}

@layer components {
  .neon-border {
    border: 1px solid rgba(0, 255, 163, 0.3);
    box-shadow: 0 0 10px rgba(0, 255, 163, 0.2);
  }

  .neon-glow {
    transition: all 0.3s ease;
  }

  .neon-glow:hover {
    box-shadow: 0 0 20px rgba(0, 255, 163, 0.5);
    border-color: rgba(0, 255, 163, 0.8);
    transform: scale(1.02);
  }

  .topic-card {
    @apply bg-card border border-border rounded-xl p-6 cursor-pointer transition-all duration-300;
    background: linear-gradient(
      135deg,
      rgba(26, 26, 26, 0.8) 0%,
      rgba(26, 26, 26, 0.4) 100%
    );
    backdrop-filter: blur(10px);
  }

  .challenge-card {
    @apply bg-card border border-border rounded-lg p-4 cursor-pointer transition-all duration-300;
    background: linear-gradient(
      135deg,
      rgba(26, 26, 26, 0.9) 0%,
      rgba(26, 26, 26, 0.5) 100%
    );
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
  }

  .challenge-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 255, 163, 0.1),
      transparent
    );
    transition: left 0.5s;
  }

  .challenge-card:hover::before {
    left: 100%;
  }

  .challenge-card:hover {
    @apply shadow-card-hover border-neon-green/50;
    transform: translateY(-2px);
  }

  .glass-card {
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .hero-text {
    background: linear-gradient(135deg, #ffffff 0%, #00ffa3 50%, #4dffdf 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .btn-primary {
    @apply bg-neon-gradient text-black font-semibold px-6 py-3 rounded-lg transition-all duration-300;
    box-shadow: 0 4px 15px rgba(0, 255, 163, 0.3);
  }

  .btn-primary:hover {
    @apply shadow-neon-strong;
    transform: translateY(-2px);
  }

  .progress-bar {
    @apply w-full h-2 bg-secondary rounded-full overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-neon-gradient rounded-full transition-all duration-1000 ease-out;
  }

  .difficulty-easy {
    @apply bg-green-500/20 text-green-400 border border-green-500/30;
  }

  .difficulty-medium {
    @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
  }

  .difficulty-hard {
    @apply bg-red-500/20 text-red-400 border border-red-500/30;
  }

  .company-logo {
    @apply w-12 h-12 rounded-lg bg-secondary flex items-center justify-center text-2xl;
    background: linear-gradient(
      135deg,
      rgba(26, 26, 26, 0.8) 0%,
      rgba(26, 26, 26, 0.4) 100%
    );
  }

  .vs-code-panel {
    @apply bg-[#1E1E1E] border-l border-neon-green/20;
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.5);
  }

  .code-line {
    @apply border-l-2 border-transparent pl-4;
    transition: all 0.2s ease;
  }

  .code-line:hover {
    @apply border-neon-green/50 bg-neon-green/5;
  }

  .typewriter {
    overflow: hidden;
    border-right: 2px solid #00ffa3;
    white-space: nowrap;
    animation: typing 2s steps(40, end), blink-caret 0.75s step-end infinite;
  }

  .floating-glow {
    position: relative;
  }

  .floating-glow::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(45deg, #00ffa3, #4dffdf, #00ffa3);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .floating-glow:hover::after {
    opacity: 1;
  }
}

@layer utilities {
  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  @keyframes typing {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }

  @keyframes blink-caret {
    from,
    to {
      border-color: transparent;
    }
    50% {
      border-color: #00ffa3;
    }
  }

  .animate-typing {
    animation: typing 2s steps(40, end), blink-caret 0.75s step-end infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  @keyframes glow-pulse {
    0%,
    100% {
      box-shadow: 0 0 20px rgba(0, 255, 163, 0.5);
    }
    50% {
      box-shadow: 0 0 30px rgba(0, 255, 163, 0.8);
    }
  }
}

.hero .btn-primary {
  @apply text-[rgb(13,13,13)] rounded-none;
}

.hero .text-foreground {
  color: #ffffff;
}

.hero .bg-secondary {
  background: #1a1a1a;
}

.hero .bg-background {
  background: rgb(13, 13, 13);
  color: #ffffff;
}

.hero .text-muted-foreground {
  color: #a0a0a0;
}
