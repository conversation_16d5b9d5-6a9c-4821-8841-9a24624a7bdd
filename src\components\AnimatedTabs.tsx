import React, { useState } from "react";
import { motion } from "motion/react";
import { Tabs, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Problem } from "@/api/problems";

const AnimatedTabs = ({ tabs, defaultTab }) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="relative bg-secondary rounded-none border border-border">
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.id}
            value={tab.id}
            className="relative z-10 px-4 py-2 text-sm font-medium transition-colors data-[state=active]:text-white data-[state=active]:bg-transparent"
          >
            {tab.label}
            {activeTab === tab.id && (
              <motion.div
                layoutId="activeTab"
                className="absolute inset-0 bg-neon-green/20 rounded-none border border-neon-green/50"
                initial={false}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                }}
              />
            )}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map((tab) => (
        <TabsContent key={tab.id} value={tab.id} className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {tab.content}
          </motion.div>
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default AnimatedTabs;
