<!-- Tasks -->

# landing page

We challenge means problems :- whenever we tall about challenge just tall about problem
active coder
total problems
total companies questions list over there
Featured section me problem of the day
Trending :- pick most submission of the problem top 3 only
Sheet :- pick most liked sheet on the page top 6 only
topic to explore :
#hashtag filtered problems
live solving and leaderboard [daily, weekly,all time]

Rewords :- xp based rewords
[
{ "level": 1, "requiredXP": 0, "tier": "Bronze" },
{ "level": 2, "requiredXP": 50, "tier": "Bronze" },
{ "level": 3, "requiredXP": 100, "tier": "Bronze" },
{ "level": 4, "requiredXP": 150, "tier": "Bronze" },
{ "level": 5, "requiredXP": 200, "tier": "Bronze" },
{ "level": 6, "requiredXP": 400, "tier": "Silver" },
{ "level": 7, "requiredXP": 600, "tier": "Silver" },
{ "level": 8, "requiredXP": 850, "tier": "Silver" },
{ "level": 9, "requiredXP": 1100, "tier": "Silver" },
{ "level": 10, "requiredXP": 1500, "tier": "Silver" },
{ "level": 11, "requiredXP": 1800, "tier": "Gold" },
{ "level": 12, "requiredXP": 2100, "tier": "Gold" },
{ "level": 13, "requiredXP": 2400, "tier": "Gold" },
{ "level": 14, "requiredXP": 2700, "tier": "Gold" },
{ "level": 15, "requiredXP": 3000, "tier": "Gold" },
{ "level": 16, "requiredXP": 3300, "tier": "Gold" },
{ "level": 17, "requiredXP": 3600, "tier": "Gold" },
{ "level": 18, "requiredXP": 3900, "tier": "Gold" },
{ "level": 19, "requiredXP": 4200, "tier": "Gold" },
{ "level": 20, "requiredXP": 4500, "tier": "Gold" },
{ "level": 21, "requiredXP": 5000, "tier": "Platinum" },
{ "level": 22, "requiredXP": 5500, "tier": "Platinum" },
{ "level": 23, "requiredXP": 6000, "tier": "Platinum" },
{ "level": 24, "requiredXP": 6500, "tier": "Platinum" },
{ "level": 25, "requiredXP": 7000, "tier": "Platinum" },
{ "level": 26, "requiredXP": 7500, "tier": "Platinum" },
{ "level": 27, "requiredXP": 8000, "tier": "Platinum" },
{ "level": 28, "requiredXP": 8500, "tier": "Platinum" },
{ "level": 29, "requiredXP": 9000, "tier": "Platinum" },
{ "level": 30, "requiredXP": 9500, "tier": "Platinum" },
{ "level": 31, "requiredXP": 10200, "tier": "Diamond" },
{ "level": 32, "requiredXP": 10900, "tier": "Diamond" },
{ "level": 33, "requiredXP": 11600, "tier": "Diamond" },
{ "level": 34, "requiredXP": 12300, "tier": "Diamond" },
{ "level": 35, "requiredXP": 13000, "tier": "Diamond" },
{ "level": 36, "requiredXP": 13700, "tier": "Diamond" },
{ "level": 37, "requiredXP": 14400, "tier": "Diamond" },
{ "level": 38, "requiredXP": 15100, "tier": "Diamond" },
{ "level": 39, "requiredXP": 15800, "tier": "Diamond" },
{ "level": 40, "requiredXP": 16500, "tier": "Diamond" },
{ "level": 41, "requiredXP": 17500, "tier": "Master" },
{ "level": 42, "requiredXP": 18500, "tier": "Master" },
{ "level": 43, "requiredXP": 19500, "tier": "Master" },
{ "level": 44, "requiredXP": 20500, "tier": "Master" },
{ "level": 45, "requiredXP": 21500, "tier": "Master" },
{ "level": 46, "requiredXP": 22500, "tier": "Master" },
{ "level": 47, "requiredXP": 23500, "tier": "Master" },
{ "level": 48, "requiredXP": 24500, "tier": "Master" },
{ "level": 49, "requiredXP": 25500, "tier": "Master" },
{ "level": 50, "requiredXP": 26500, "tier": "Grandmaster" }
]

"problemXP": {
"Easy": 10,
"Medium": 25,
"Hard": 50,
"Challenge": 100
},
"badges": [
{
"id": "first_blood",
"name": "First Blood",
"description": "Solve your first problem"
},
{
"id": "consistency_king",
"name": "Consistency King",
"description": "Solve a problem daily for 7 days"
},
{
"id": "night_owl",
"name": "Night Owl",
"description": "Solve a problem after midnight"
},
{
"id": "no_help_hero",
"name": "No Help Hero",
"description": "Solve 10 hard problems without hints or viewing solutions"
}
],
"antiAbuseChecks": {
"allowXPIf": {
"usedHint": false,
"solutionViewed": false
},
"xpAwardedOncePerProblem": true
}

# Dashboard
- total solved problem 
- last week total solved problem
- current streak
- contest max rating
- code reviews
- level of progress

filter problems like random, problem of the day, difficulty, status, tag


# Problems
- create problem
- filter problems
- all problems

# sheets
my sheet 
public sheets
create sheet 
like a sheet
share a sheet

# profile
name username tire bio links profile pic
update profile image
update profile info

total problems solved
current streak
max contest rating
code reviews

level of progress
achievements badges
contribution activity
languages
community state - [profile views, code review, sheet likes]

# contests
  only admin create contest 
  we have time contest name total participants total problems
  total xp you get and 
  there is upcoming contest and past contests also

# Interview Mock
total interview you gave
avarage socre
best performace
start mock interview

most recent interview list you can view report

################################## TASKS #########################################

✅ LANDING PAGE Tasks
 Replace every occurrence of "challenge" with "problem" ✅

 Show total active coders ✅

 Display total problems✅

 List total company-specific question lists ✅

 Add Featured Section → "Problem of the Day ✅

 Add Trending Problems Section → top 3 with most submissions

 Add Top Sheets Section → top 6 most liked sheets

 Add Topics to Explore → filter by #hashtags

 Add Live Solving & Leaderboard with views:

Daily

Weekly

All Time

🏆 XP, REWARDS & BADGES SYSTEM
 Implement XP-Based Rewards Logic

 Map XP to Levels (Bronze → Grandmaster)

 XP Mapping:

Easy → 10 XP

Medium → 25 XP

Hard → 50 XP

Challenge → 100 XP

 Add Badge System:

First Blood

Consistency King

Night Owl

No Help Hero

 Add Anti-Abuse XP Check:

XP awarded only if no hint or solution used

XP granted only once per problem

📊 DASHBOARD
 Show Total Solved Problems

 Show Last Week’s Total Solved Problems

 Show Current Streak

 Show Max Contest Rating

 Show Total Code Reviews

 Display Level & Progress Bar

 Add Filters: Random | Problem of the Day | Difficulty | Status | Tag

🧠 PROBLEMS MODULE
 Create New Problem (Admin only)

 Filter Problems (tag, difficulty, etc.)

 View All Problems Page

📋 SHEETS MODULE
 Create Sheet

 View Public Sheets

 View My Sheets

 Like a Sheet

 Share a Sheet (copy link, etc.)

🙍‍♂️ PROFILE PAGE
 View & Edit: Name, Username, Tier, Bio, Social Links, Profile Pic

 Update Profile Picture

 Update Profile Info

 Show:

Total Problems Solved

Current Streak

Max Contest Rating

Code Reviews

Level & Progress

Badges

Achievement List

Contribution Activity Graph

Languages Used

Community Stats (profile views, sheet likes, etc.)

🏁 CONTESTS MODULE
 Admin Creates Contest (with timer, name, problems, etc.)

 Show Total Participants & Total Problems

 Track Total XP Gained per Contest

 Separate Upcoming vs Past Contests View

🎯 INTERVIEW MOCK SYSTEM
 Track Total Interviews Given

 Track Average Score

 Track Best Performance

 Start Mock Interview Button

 List Recent Interviews with View Report Option

+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

 <!-- Change the color of the create page  -->
 <!-- fork or share remove the button and inc the size of progress bar -->
 <!-- leaderboard alginment -->
 <!-- if we get a one day we add a terminal -->


Explore done ✅ 
search problems ✅ 
explore by tags ✅ 
Problem of the day ✅ 
explore challenges ✅ 
  1. trending ✅
  2. daily challenges ✅
  3. problem sheet most liked ❌ 

all tags with problems ✅ 
trending challenges ✅ 
leaderboard ✅ 
filter work for problems ✅ 
lots of request on index page ✅ 

======= working on sheets ======
Sheet creation form done ✅ 
sheet creation done ✅ 
get all my sheet or public sheet ✅ 
update the sheet ✅ 


========== dashboard ===========
all things work in dashboard ✅ 


============= interview page =========



======== task ===============
before 8 pm i gona by the domain name
add a demo button to get direct login 




